.PHONY: all run_dev_web run_dev_mobile run_unit clean upgrade lint format build_dev_mobile help 

all: lint format build_runner

# Adding a help file: https://gist.github.com/prwhite/8168133#gistcomment-1313022
help: ## This help dialog.
	@IFS=$$'\n' ; \
	help_lines=(`fgrep -h "##" $(MAKEFILE_LIST) | fgrep -v fgrep | sed -e 's/\\$$//'`); \
	for help_line in $${help_lines[@]}; do \
		IFS=$$'#' ; \
		help_split=($$help_line) ; \
		help_command=`echo $${help_split[0]} | sed -e 's/^ *//' -e 's/ *$$//'` ; \
		help_info=`echo $${help_split[2]} | sed -e 's/^ *//' -e 's/ *$$//'` ; \
		printf "%-30s %s\n" $$help_command $$help_info ; \
	done

run_unit: ## Runs unit tests
	@echo "╠ Running the tests"
	@flutter test || (echo "Error while running tests"; exit 1)

clean: ## Cleans the environment
	@echo "╠ Cleaning the project..."
	@rm -rf pubspec.lock
	@flutter clean

format: ## Formats the code with line 120
	@echo "╠ Formatting the code"
	@dart format  .

lint: ## Lints the code
	@echo "╠ Verifying code..."
	@dart analyze . || (echo "Error in project"; exit 1)

upgrade: clean ## Upgrades dependencies
	@echo "╠ Upgrading dependencies..."
	@flutter pub upgrade

build_format:  ## generates codes for freezed and json serialize
	@echo "╠ Running build to generate files"
	@dart run build_runner build -d
	@make format

build_runner:  ## generates codes for freezed and json serialize
	@echo "╠ Running build to generate files"
	@dart run build_runner watch build -d

build_dev_apk: 
	@echo "╠  Building the app"
	@echo "╠  flavor = development"
	@flutter build appbundle --release --flavor development -t lib/main.dart

build_prod: 
	@echo "╠  Building the app"
	@echo "╠  flavor = production"
	@flutter build appbundle --release --flavor production -t lib/main.dart

delete_locale_branches:
	@echo "╠  remove all local branches except main|development|production branches"
	@git branch | egrep -v "(^\*|main|development|production)" | xargs git branch -D

build_web: ## Builds the Flutter web version
	@echo "╠ Building the web version"
	@flutter build web --release

deploy_firebase: ## Deploys the web app to Firebase
	@echo "╠ Deploying to Firebase"
	@firebase deploy --only hosting

deploy_web: ## Builds and deploys the web app to Firebase in one command
	@make build_web
	@make deploy_firebase