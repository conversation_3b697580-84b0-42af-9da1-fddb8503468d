# Slang Translation Migration Guide

This guide helps you migrate from hardcoded strings to slang translations.

## ✅ What's Already Done

1. **Slang Setup**: Configured slang in `pubspec.yaml` and added Makefile commands
2. **Translation Files**: Created comprehensive `en.i18n.json` and `tr.i18n.json` files
3. **App Integration**: Updated `app.dart` and localization providers
4. **Sample Migrations**: Migrated priority enums, task types, and device providers

## 🔧 Makefile Commands Added

```bash
make slang          # Generate translations from JSON
make slang_watch    # Watch for changes and regenerate
make slang_build    # Generate translations and format code
```

## 📁 Translation Structure

The translations are organized in nested objects:

```json
{
  "common": { "name": "Name", "search": "Search", ... },
  "priority": { "low": "Low", "medium": "Medium", "high": "High" },
  "taskType": { "visit": "Visit", "collection": "Collection", ... },
  "device": { "brands": { "teltonika": "Teltonika", ... }, ... },
  "vehicle": { "addVehicle": "Add Vehicle", ... },
  "customer": { "customers": "Customers", ... },
  "form": { "openForm": "Open Form", ... },
  "area": { "areaEntry": "Area Entry", ... },
  "emergency": { "emergencyContactName": "Emergency Contact Name", ... }
}
```

## 🚀 How to Use Slang

### Method 1: Simple (Global `t`)
```dart
import 'package:smart_team_web/i18n/strings.g.dart';

String title = t.appTitle;
String priority = t.priority.low;
String brand = t.device.brands.teltonika;
```

### Method 2: Context-aware (Recommended for UI)
```dart
import 'package:smart_team_web/i18n/strings.g.dart';

@override
Widget build(BuildContext context) {
  return Text(context.t.appTitle); // Rebuilds when locale changes
}
```

## 📝 Migration Steps

### 1. Find Files with Hardcoded Strings
```bash
find lib/ -name "*.dart" -exec grep -l "\.hardcoded" {} \;
```

### 2. For Each File:

1. **Add slang import:**
   ```dart
   import 'package:smart_team_web/i18n/strings.g.dart';
   ```

2. **Remove hardcoded import:**
   ```dart
   // Remove this line:
   import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
   ```

3. **Replace hardcoded strings:**
   ```dart
   // Before:
   'Düşük'.hardcoded
   
   // After:
   context.t.priority.low  // or t.priority.low
   ```

### 3. Common Replacements

| Hardcoded | Slang Translation |
|-----------|-------------------|
| `'Ad'.hardcoded` | `context.t.common.name` |
| `'Ara'.hardcoded` | `context.t.common.search` |
| `'Düşük'.hardcoded` | `context.t.priority.low` |
| `'Ziyaret'.hardcoded` | `context.t.taskType.visit` |
| `'Teltonika'.hardcoded` | `context.t.device.brands.teltonika` |
| `'A Müşterisi'.hardcoded` | `context.t.customer.customerA` |

### 4. Add New Translations

1. **Add to JSON files:**
   ```json
   // lib/i18n/en.i18n.json
   {
     "newSection": {
       "newKey": "New English Text"
     }
   }
   
   // lib/i18n/tr.i18n.json  
   {
     "newSection": {
       "newKey": "Yeni Türkçe Metin"
     }
   }
   ```

2. **Regenerate:**
   ```bash
   make slang
   ```

3. **Use in code:**
   ```dart
   Text(context.t.newSection.newKey)
   ```

## 🎯 Migration Priority

1. **High Priority**: Enums and providers (already done)
2. **Medium Priority**: UI components and widgets
3. **Low Priority**: Mock data and test strings

## 📊 Progress Tracking

- **Total hardcoded strings**: 636
- **Migrated**: ~50 (enums, providers)
- **Remaining**: ~586

## 🔍 Migration Helper

Run the migration helper script:
```bash
dart scripts/migrate_hardcoded_strings.dart
```

This script shows common replacement patterns and tips.

## ⚠️ Important Notes

1. **Context vs Global**: Use `context.t.*` for UI widgets, `t.*` for simple cases
2. **Imports**: Always import slang, remove hardcoded extension import
3. **Regeneration**: Run `make slang` after adding new translations
4. **Testing**: Test language switching after each migration batch

## 🧪 Testing

After migration:
1. Run the app: `flutter run -d chrome`
2. Test language switching with the language switcher widget
3. Verify all translated strings appear correctly in both languages

## 📚 Examples

See the migrated files for examples:
- `lib/src/shared/extensions/form_priority_enum_extensions.dart`
- `lib/src/job_list/enum/task_type_enum.dart`
- `lib/src/home/<USER>/admin_add_new_device_provider.dart`
- `lib/src/devices/application/new_device_provider.dart`
