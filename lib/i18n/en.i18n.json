{"appTitle": "Smart Team Web", "start": "Get Started", "termsOfServiceAndPrivacyPolicy": "<link href=\"termsOfService\">Terms of Service</link> and <link href=\"privacyPolicy\">Privacy Policy</link>", "registerAgreement": "I confirm that I have read and accepted the <link>Membership Agreement</link>, <link>Data Protection and Business Policy</link>, <link>Customer Clarification Text</link>, <link>Privacy and Cookie Policy</link>.", "common": {"name": "Name", "description": "Description", "active": "Active", "inactive": "Inactive", "search": "Search", "add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "select": "Select", "all": "All", "none": "None", "online": "Online", "offline": "Offline", "admin": "Admin", "user": "User"}, "priority": {"low": "Low", "medium": "Medium", "high": "High"}, "taskType": {"visit": "Visit", "collection": "Collection", "service": "Service", "onlineMeeting": "Online Meeting", "phoneCall": "Phone Call"}, "device": {"brands": {"teltonika": "Teltonika", "armoli": "Armoli", "kingwoiot": "<PERSON><PERSON><PERSON>"}, "models": {"modelA": "Model A", "modelB": "Model B", "modelC": "Model C"}, "simOperators": {"vodafone": "Vodafone", "turkcell": "Turkcell", "turkTelekom": "Türk Telekom"}, "addDevice": "Add <PERSON>", "deviceList": "Device List", "activationDate": "Activation Date"}, "vehicle": {"addVehicle": "Add Vehicle", "vehicleList": "Vehicle List", "vehicleBrand": "Vehicle Brand", "vehicleModel": "Vehicle Model", "vehicleType": "Vehicle Type", "brands": {"mercedes": "Mercedes", "bmw": "BMW", "audi": "Audi"}}, "customer": {"customers": "Customers", "addCustomer": "Add Customer", "customerA": "Customer A", "customerB": "Customer B", "customerC": "Customer C", "phoneNumber": "Phone Number", "address": "Address", "openAddress": "Open Address", "findMyLocation": "Find My Location", "fillAllFields": "Please fill all fields"}, "group": {"groupA": "Group A", "groupB": "Group B", "groupC": "Group C", "groupD": "Group D"}, "dashboard": {"todoList": "Todo List", "instantMovements": "Instant Movements", "instantStoppage": "Instant Stoppage"}, "form": {"openForm": "Open Form", "openFormDescription": "Open form, visible to everyone and cannot be assigned to individuals", "deleteFormConfirm": "Are you sure you want to delete the form titled '{title}'?", "requiredQuestion": "* Indicates a required question", "addOption": "Add option"}, "area": {"areaEntry": "Area Entry", "areaWaiting": "Area Waiting", "areaExit": "Area Exit", "editArea": "Edit Area", "areaName": "Area Name", "areaType": "Area Type"}, "emergency": {"emergencyContactName": "Emergency Contact Name", "emergencyPhone": "Emergency Phone", "emergencyContact": "Emergency Contact"}, "validation": {"linkCouldNotOpen": "Link could not be opened", "phoneNumberExample": "+90 555 555 55 55", "phoneNumberShort": "555 555 55 55"}, "copyright": "Smart Location Technologies Inc. © 2025"}