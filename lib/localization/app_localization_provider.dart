import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/app.dart';
import 'package:smart_team_web/src/shared/extensions/app_locale_extension.dart';

final languageProvider =
    StateNotifierProvider<LanguageNotifier, AppLocale>((ref) {
  return LanguageNotifier();
});

class LanguageNotifier extends StateNotifier<AppLocale> {
  LanguageNotifier() : super(AppLocale.tr);

  void toggleLocale() {
    state = state == AppLocale.en ? AppLocale.tr : AppLocale.en;
  }

  void setLocale(AppLocale locale) {
    state = locale;
  }
}

Locale get appLocale {
  assert(appNavigatorKey.currentContext != null,
      'Do not use appLocale without a valid context!');
  return Localizations.localeOf(appNavigatorKey.currentContext!);
}
