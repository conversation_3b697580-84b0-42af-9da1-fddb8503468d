import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_tr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'localization/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('tr'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Smart Team Web'**
  String get appTitle;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get start;

  /// No description provided for @termsOfServiceAndPrivacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'<link href=\"termsOfService\">Terms of Service</link> and <link href=\"privacyPolicy\">Privacy Policy</link>'**
  String get termsOfServiceAndPrivacyPolicy;

  /// No description provided for @registerAgreement.
  ///
  /// In en, this message translates to:
  /// **'I confirm that I have read and accepted the <link>Membership Agreement</link>, <link>Data Protection and Business Policy</link>, <link>Customer Clarification Text</link>, <link>Privacy and Cookie Policy</link>.'**
  String get registerAgreement;

  /// No description provided for @minCharacterError.
  ///
  /// In en, this message translates to:
  /// **'The number of characters can be at least {charLength}'**
  String minCharacterError(int charLength);

  /// No description provided for @dashboard.
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// No description provided for @customerId.
  ///
  /// In en, this message translates to:
  /// **'Customer Id'**
  String get customerId;

  /// No description provided for @rules.
  ///
  /// In en, this message translates to:
  /// **'Rules'**
  String get rules;

  /// No description provided for @ruleList.
  ///
  /// In en, this message translates to:
  /// **'Rule List'**
  String get ruleList;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// No description provided for @addNewRule.
  ///
  /// In en, this message translates to:
  /// **'Add New Rule'**
  String get addNewRule;

  /// No description provided for @newRule.
  ///
  /// In en, this message translates to:
  /// **'New Rule'**
  String get newRule;

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Select...'**
  String get select;

  /// No description provided for @speedViolation.
  ///
  /// In en, this message translates to:
  /// **'Excessive Speed Violation'**
  String get speedViolation;

  /// No description provided for @stopTimeViolation.
  ///
  /// In en, this message translates to:
  /// **'Instant Stop Time Violation'**
  String get stopTimeViolation;

  /// No description provided for @movementTimeViolation.
  ///
  /// In en, this message translates to:
  /// **'Instantaneous Movement Time Violation'**
  String get movementTimeViolation;

  /// No description provided for @dailyMaxStopTimeViolation.
  ///
  /// In en, this message translates to:
  /// **'Daily Maximum Stopping Time Violation'**
  String get dailyMaxStopTimeViolation;

  /// No description provided for @dailyMovementTimeViolation.
  ///
  /// In en, this message translates to:
  /// **'Daily Movement Time Violation'**
  String get dailyMovementTimeViolation;

  /// No description provided for @areaEntryExitViolation.
  ///
  /// In en, this message translates to:
  /// **'Area Entry/Exit Violation'**
  String get areaEntryExitViolation;

  /// No description provided for @suddenAcceleration.
  ///
  /// In en, this message translates to:
  /// **'Sudden Acceleration'**
  String get suddenAcceleration;

  /// No description provided for @suddenSlowdown.
  ///
  /// In en, this message translates to:
  /// **'Sudden Slowdown'**
  String get suddenSlowdown;

  /// No description provided for @phoneCall.
  ///
  /// In en, this message translates to:
  /// **'Phone Call'**
  String get phoneCall;

  /// No description provided for @workingTimeDelayViolation.
  ///
  /// In en, this message translates to:
  /// **'Working Time Delay Violation'**
  String get workingTimeDelayViolation;

  /// No description provided for @fatigueViolation.
  ///
  /// In en, this message translates to:
  /// **'Fatigue Violation'**
  String get fatigueViolation;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @customerList.
  ///
  /// In en, this message translates to:
  /// **'Customer List'**
  String get customerList;

  /// No description provided for @customers.
  ///
  /// In en, this message translates to:
  /// **'Customers'**
  String get customers;

  /// No description provided for @customerName.
  ///
  /// In en, this message translates to:
  /// **'Customer Name'**
  String get customerName;

  /// No description provided for @contactName.
  ///
  /// In en, this message translates to:
  /// **'Contact Name'**
  String get contactName;

  /// No description provided for @deviceName.
  ///
  /// In en, this message translates to:
  /// **'Device Name'**
  String get deviceName;

  /// No description provided for @gsm.
  ///
  /// In en, this message translates to:
  /// **'GSM'**
  String get gsm;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @province.
  ///
  /// In en, this message translates to:
  /// **'Province'**
  String get province;

  /// No description provided for @district.
  ///
  /// In en, this message translates to:
  /// **'District'**
  String get district;

  /// No description provided for @addNewCustomer.
  ///
  /// In en, this message translates to:
  /// **'Add New Customer'**
  String get addNewCustomer;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @active.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// No description provided for @potential.
  ///
  /// In en, this message translates to:
  /// **'Potential'**
  String get potential;

  /// No description provided for @authorizedName.
  ///
  /// In en, this message translates to:
  /// **'Authorized Name'**
  String get authorizedName;

  /// No description provided for @authorizedEmail.
  ///
  /// In en, this message translates to:
  /// **'Authorized Email'**
  String get authorizedEmail;

  /// No description provided for @contactGSM.
  ///
  /// In en, this message translates to:
  /// **'Contact GSM'**
  String get contactGSM;

  /// No description provided for @neighborhood.
  ///
  /// In en, this message translates to:
  /// **'Neighborhood'**
  String get neighborhood;

  /// No description provided for @authorizedGSM.
  ///
  /// In en, this message translates to:
  /// **'Authorized GSM'**
  String get authorizedGSM;

  /// No description provided for @contactEmail.
  ///
  /// In en, this message translates to:
  /// **'Contact Email'**
  String get contactEmail;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @postalCode.
  ///
  /// In en, this message translates to:
  /// **'Postal Code'**
  String get postalCode;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @devices.
  ///
  /// In en, this message translates to:
  /// **'Devices'**
  String get devices;

  /// No description provided for @deviceList.
  ///
  /// In en, this message translates to:
  /// **'Device List'**
  String get deviceList;

  /// No description provided for @user.
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get user;

  /// No description provided for @applicationInstalled.
  ///
  /// In en, this message translates to:
  /// **'Application Installed'**
  String get applicationInstalled;

  /// No description provided for @brand.
  ///
  /// In en, this message translates to:
  /// **'Brand'**
  String get brand;

  /// No description provided for @model.
  ///
  /// In en, this message translates to:
  /// **'Model'**
  String get model;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @newDevice.
  ///
  /// In en, this message translates to:
  /// **'New Device'**
  String get newDevice;

  /// No description provided for @locationClosureAuthorization.
  ///
  /// In en, this message translates to:
  /// **'Location Closure Authorization'**
  String get locationClosureAuthorization;

  /// No description provided for @companyName.
  ///
  /// In en, this message translates to:
  /// **'Company Name'**
  String get companyName;

  /// No description provided for @closedPortfolio.
  ///
  /// In en, this message translates to:
  /// **'Closed Portfolio'**
  String get closedPortfolio;

  /// No description provided for @nameSurname.
  ///
  /// In en, this message translates to:
  /// **'Name Surname'**
  String get nameSurname;

  /// No description provided for @device.
  ///
  /// In en, this message translates to:
  /// **'Device'**
  String get device;

  /// No description provided for @group.
  ///
  /// In en, this message translates to:
  /// **'Group'**
  String get group;

  /// No description provided for @calendarColor.
  ///
  /// In en, this message translates to:
  /// **'Calendar Color'**
  String get calendarColor;

  /// No description provided for @reports.
  ///
  /// In en, this message translates to:
  /// **'Reports'**
  String get reports;

  /// No description provided for @calendar.
  ///
  /// In en, this message translates to:
  /// **'Calendar'**
  String get calendar;

  /// No description provided for @jobList.
  ///
  /// In en, this message translates to:
  /// **'Job List'**
  String get jobList;

  /// No description provided for @forms.
  ///
  /// In en, this message translates to:
  /// **'Forms'**
  String get forms;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @userName.
  ///
  /// In en, this message translates to:
  /// **'User Name'**
  String get userName;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @otp.
  ///
  /// In en, this message translates to:
  /// **'OTP'**
  String get otp;

  /// No description provided for @signingIn.
  ///
  /// In en, this message translates to:
  /// **'Signing In'**
  String get signingIn;

  /// No description provided for @enter.
  ///
  /// In en, this message translates to:
  /// **'Enter'**
  String get enter;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @wholeTeam.
  ///
  /// In en, this message translates to:
  /// **'Whole Team'**
  String get wholeTeam;

  /// No description provided for @team.
  ///
  /// In en, this message translates to:
  /// **'Team'**
  String get team;

  /// No description provided for @exportExcel.
  ///
  /// In en, this message translates to:
  /// **'Export to Excel'**
  String get exportExcel;

  /// No description provided for @exportPdf.
  ///
  /// In en, this message translates to:
  /// **'Export to PDF'**
  String get exportPdf;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @teamGroup.
  ///
  /// In en, this message translates to:
  /// **'Team/Group'**
  String get teamGroup;

  /// No description provided for @dataTime.
  ///
  /// In en, this message translates to:
  /// **'Data Time'**
  String get dataTime;

  /// No description provided for @headAddress.
  ///
  /// In en, this message translates to:
  /// **'Head Address'**
  String get headAddress;

  /// No description provided for @speed.
  ///
  /// In en, this message translates to:
  /// **'Speed'**
  String get speed;

  /// No description provided for @drivingTot.
  ///
  /// In en, this message translates to:
  /// **'Driving Tot.(km)'**
  String get drivingTot;

  /// No description provided for @movingTot.
  ///
  /// In en, this message translates to:
  /// **'Moving Tot.(km)'**
  String get movingTot;

  /// No description provided for @person.
  ///
  /// In en, this message translates to:
  /// **'Person'**
  String get person;

  /// No description provided for @animation.
  ///
  /// In en, this message translates to:
  /// **'Animation'**
  String get animation;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @noEventsToday.
  ///
  /// In en, this message translates to:
  /// **'There are no events for this day!'**
  String get noEventsToday;

  /// No description provided for @instantStatus.
  ///
  /// In en, this message translates to:
  /// **'Instant Status'**
  String get instantStatus;

  /// No description provided for @instantStop.
  ///
  /// In en, this message translates to:
  /// **'Instant\nStop'**
  String get instantStop;

  /// No description provided for @instantAct.
  ///
  /// In en, this message translates to:
  /// **'Instant\nAction'**
  String get instantAct;

  /// No description provided for @searchNameTask.
  ///
  /// In en, this message translates to:
  /// **'Search (Name or Task)'**
  String get searchNameTask;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @newTask.
  ///
  /// In en, this message translates to:
  /// **'New Task'**
  String get newTask;

  /// No description provided for @newUser.
  ///
  /// In en, this message translates to:
  /// **'New User'**
  String get newUser;

  /// No description provided for @task.
  ///
  /// In en, this message translates to:
  /// **'Task'**
  String get task;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @allJobs.
  ///
  /// In en, this message translates to:
  /// **'All Jobs'**
  String get allJobs;

  /// No description provided for @reportType.
  ///
  /// In en, this message translates to:
  /// **'Report Type'**
  String get reportType;

  /// No description provided for @importantJobs.
  ///
  /// In en, this message translates to:
  /// **'Important Jobs'**
  String get importantJobs;

  /// No description provided for @completedJobs.
  ///
  /// In en, this message translates to:
  /// **'Completed Jobs'**
  String get completedJobs;

  /// No description provided for @unfinishedJobs.
  ///
  /// In en, this message translates to:
  /// **'Unfinished Business'**
  String get unfinishedJobs;

  /// No description provided for @movementReport.
  ///
  /// In en, this message translates to:
  /// **'Movement Report'**
  String get movementReport;

  /// No description provided for @timeReport.
  ///
  /// In en, this message translates to:
  /// **'Time Report'**
  String get timeReport;

  /// No description provided for @startTime.
  ///
  /// In en, this message translates to:
  /// **'Start Time'**
  String get startTime;

  /// No description provided for @endTime.
  ///
  /// In en, this message translates to:
  /// **'End Time'**
  String get endTime;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'monday'**
  String get monday;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// No description provided for @sunday.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// No description provided for @outOfHours.
  ///
  /// In en, this message translates to:
  /// **'Out of Hours'**
  String get outOfHours;

  /// No description provided for @pick.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get pick;

  /// No description provided for @selectColor.
  ///
  /// In en, this message translates to:
  /// **'Select Color'**
  String get selectColor;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'Okay'**
  String get ok;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @myProfile.
  ///
  /// In en, this message translates to:
  /// **'My Profile'**
  String get myProfile;

  /// No description provided for @exit.
  ///
  /// In en, this message translates to:
  /// **'Exit'**
  String get exit;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// No description provided for @changePassword.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePassword;

  /// No description provided for @oldPass.
  ///
  /// In en, this message translates to:
  /// **'Old Password'**
  String get oldPass;

  /// No description provided for @newPass.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPass;

  /// No description provided for @newPassAgain.
  ///
  /// In en, this message translates to:
  /// **'New Password Again'**
  String get newPassAgain;

  /// No description provided for @required.
  ///
  /// In en, this message translates to:
  /// **'Required'**
  String get required;

  /// No description provided for @dailyMovementDistanceInKm.
  ///
  /// In en, this message translates to:
  /// **'Daily Movement Distance (km)'**
  String get dailyMovementDistanceInKm;

  /// No description provided for @distance.
  ///
  /// In en, this message translates to:
  /// **'Distance'**
  String get distance;

  /// No description provided for @duration.
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get duration;

  /// No description provided for @maxSpeedInKm.
  ///
  /// In en, this message translates to:
  /// **'Maximum Speed(km)'**
  String get maxSpeedInKm;

  /// No description provided for @dailyDrivingDistanceInKm.
  ///
  /// In en, this message translates to:
  /// **'Daily Driving Distance (km)'**
  String get dailyDrivingDistanceInKm;

  /// No description provided for @numberDailyAct.
  ///
  /// In en, this message translates to:
  /// **'Number of Daily Activities'**
  String get numberDailyAct;

  /// No description provided for @actType.
  ///
  /// In en, this message translates to:
  /// **'Activity Type'**
  String get actType;

  /// No description provided for @eventType.
  ///
  /// In en, this message translates to:
  /// **'Event Type'**
  String get eventType;

  /// No description provided for @contentType.
  ///
  /// In en, this message translates to:
  /// **'Content Type'**
  String get contentType;

  /// No description provided for @conclusion.
  ///
  /// In en, this message translates to:
  /// **'Conclusion'**
  String get conclusion;

  /// No description provided for @content.
  ///
  /// In en, this message translates to:
  /// **'Content'**
  String get content;

  /// No description provided for @contact.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact;

  /// No description provided for @company.
  ///
  /// In en, this message translates to:
  /// **'Company'**
  String get company;

  /// No description provided for @beginning.
  ///
  /// In en, this message translates to:
  /// **'Beginning'**
  String get beginning;

  /// No description provided for @finish.
  ///
  /// In en, this message translates to:
  /// **'End'**
  String get finish;

  /// No description provided for @totalViolation.
  ///
  /// In en, this message translates to:
  /// **'Total Violation'**
  String get totalViolation;

  /// No description provided for @numberDevicesNoData.
  ///
  /// In en, this message translates to:
  /// **'Number of Devices with No Data'**
  String get numberDevicesNoData;

  /// No description provided for @time.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// No description provided for @reason.
  ///
  /// In en, this message translates to:
  /// **'Reason'**
  String get reason;

  /// No description provided for @gps.
  ///
  /// In en, this message translates to:
  /// **'GPS'**
  String get gps;

  /// No description provided for @inAppLocationSharing.
  ///
  /// In en, this message translates to:
  /// **'In-app Location Sharing'**
  String get inAppLocationSharing;

  /// No description provided for @jan.
  ///
  /// In en, this message translates to:
  /// **'January'**
  String get jan;

  /// No description provided for @feb.
  ///
  /// In en, this message translates to:
  /// **'February'**
  String get feb;

  /// No description provided for @march.
  ///
  /// In en, this message translates to:
  /// **'March'**
  String get march;

  /// No description provided for @april.
  ///
  /// In en, this message translates to:
  /// **'April'**
  String get april;

  /// No description provided for @may.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get may;

  /// No description provided for @june.
  ///
  /// In en, this message translates to:
  /// **'June'**
  String get june;

  /// No description provided for @july.
  ///
  /// In en, this message translates to:
  /// **'July'**
  String get july;

  /// No description provided for @aug.
  ///
  /// In en, this message translates to:
  /// **'August'**
  String get aug;

  /// No description provided for @sep.
  ///
  /// In en, this message translates to:
  /// **'September'**
  String get sep;

  /// No description provided for @oct.
  ///
  /// In en, this message translates to:
  /// **'October'**
  String get oct;

  /// No description provided for @nov.
  ///
  /// In en, this message translates to:
  /// **'November'**
  String get nov;

  /// No description provided for @dec.
  ///
  /// In en, this message translates to:
  /// **'December'**
  String get dec;

  /// No description provided for @newJob.
  ///
  /// In en, this message translates to:
  /// **'New Job'**
  String get newJob;

  /// No description provided for @showDevices.
  ///
  /// In en, this message translates to:
  /// **'Show Devices'**
  String get showDevices;

  /// No description provided for @customer.
  ///
  /// In en, this message translates to:
  /// **'Customer'**
  String get customer;

  /// No description provided for @priority.
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// No description provided for @low.
  ///
  /// In en, this message translates to:
  /// **'Low'**
  String get low;

  /// No description provided for @mid.
  ///
  /// In en, this message translates to:
  /// **'Middle'**
  String get mid;

  /// No description provided for @high.
  ///
  /// In en, this message translates to:
  /// **'High'**
  String get high;

  /// No description provided for @critical.
  ///
  /// In en, this message translates to:
  /// **'Critical'**
  String get critical;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @fieldRequired.
  ///
  /// In en, this message translates to:
  /// **'Field Required'**
  String get fieldRequired;

  /// No description provided for @enterValidId.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid customer number.'**
  String get enterValidId;

  /// No description provided for @enterValidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address.'**
  String get enterValidEmail;

  /// No description provided for @enterValidPass.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid password.'**
  String get enterValidPass;

  /// No description provided for @enterValidOTP.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid OTP code.'**
  String get enterValidOTP;

  /// No description provided for @enterValidPostalCode.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid postal code.'**
  String get enterValidPostalCode;

  /// No description provided for @enterValidName.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid name.'**
  String get enterValidName;

  /// No description provided for @enterValidPhone.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number.'**
  String get enterValidPhone;

  /// No description provided for @enterValidWebAddress.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid web address.'**
  String get enterValidWebAddress;

  /// No description provided for @enterValidIpAddress.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid server IP.'**
  String get enterValidIpAddress;

  /// No description provided for @enterValidServerPort.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid server port.'**
  String get enterValidServerPort;

  /// No description provided for @enterValidLatLng.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid lat. long. format.'**
  String get enterValidLatLng;

  /// No description provided for @enterValidBuildingDoor.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid building door no.'**
  String get enterValidBuildingDoor;

  /// No description provided for @users.
  ///
  /// In en, this message translates to:
  /// **'Users'**
  String get users;

  /// No description provided for @userDevice.
  ///
  /// In en, this message translates to:
  /// **'User Device'**
  String get userDevice;

  /// No description provided for @userType.
  ///
  /// In en, this message translates to:
  /// **'User Type'**
  String get userType;

  /// No description provided for @passive.
  ///
  /// In en, this message translates to:
  /// **'Passive'**
  String get passive;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @roleType.
  ///
  /// In en, this message translates to:
  /// **'Role Type'**
  String get roleType;

  /// No description provided for @passAgain.
  ///
  /// In en, this message translates to:
  /// **'Password(Again)'**
  String get passAgain;

  /// No description provided for @generatePass.
  ///
  /// In en, this message translates to:
  /// **'Generate Password'**
  String get generatePass;

  /// No description provided for @noDataFound.
  ///
  /// In en, this message translates to:
  /// **'No data found'**
  String get noDataFound;

  /// No description provided for @previous.
  ///
  /// In en, this message translates to:
  /// **'Previous'**
  String get previous;

  /// No description provided for @page.
  ///
  /// In en, this message translates to:
  /// **'Page'**
  String get page;

  /// No description provided for @record.
  ///
  /// In en, this message translates to:
  /// **'record'**
  String get record;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'Day'**
  String get day;

  /// No description provided for @week.
  ///
  /// In en, this message translates to:
  /// **'Week'**
  String get week;

  /// No description provided for @month.
  ///
  /// In en, this message translates to:
  /// **'Month'**
  String get month;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @forgetPass.
  ///
  /// In en, this message translates to:
  /// **'Forget Password'**
  String get forgetPass;

  /// No description provided for @waiting.
  ///
  /// In en, this message translates to:
  /// **'Waiting'**
  String get waiting;

  /// No description provided for @accepted.
  ///
  /// In en, this message translates to:
  /// **'Accepted'**
  String get accepted;

  /// No description provided for @declined.
  ///
  /// In en, this message translates to:
  /// **'Declined'**
  String get declined;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @lastDataTime.
  ///
  /// In en, this message translates to:
  /// **'Last Data Time'**
  String get lastDataTime;

  /// No description provided for @lastAddress.
  ///
  /// In en, this message translates to:
  /// **'Last Address'**
  String get lastAddress;

  /// No description provided for @dayStartAddress.
  ///
  /// In en, this message translates to:
  /// **'Day Start Address'**
  String get dayStartAddress;

  /// No description provided for @dailyDowntime.
  ///
  /// In en, this message translates to:
  /// **'Daily Downtime'**
  String get dailyDowntime;

  /// No description provided for @dailyWalkingTime.
  ///
  /// In en, this message translates to:
  /// **'Daily Walking Time'**
  String get dailyWalkingTime;

  /// No description provided for @dailyWalkingDistance.
  ///
  /// In en, this message translates to:
  /// **'Daily Walking Distance (km)'**
  String get dailyWalkingDistance;

  /// No description provided for @dailyDrivingTime.
  ///
  /// In en, this message translates to:
  /// **'Daily Driving Time'**
  String get dailyDrivingTime;

  /// No description provided for @dailyDrivingDistance.
  ///
  /// In en, this message translates to:
  /// **'Daily Driving Distance (km)'**
  String get dailyDrivingDistance;

  /// No description provided for @vehicleType.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Type'**
  String get vehicleType;

  /// No description provided for @vehicleTotalDistance.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Total Distance (km)'**
  String get vehicleTotalDistance;

  /// No description provided for @lastHour.
  ///
  /// In en, this message translates to:
  /// **'Last {number} hour'**
  String lastHour(Object number);

  /// No description provided for @timeInterval.
  ///
  /// In en, this message translates to:
  /// **'Time Interval'**
  String get timeInterval;

  /// No description provided for @showBusinessHours.
  ///
  /// In en, this message translates to:
  /// **'Show business hours'**
  String get showBusinessHours;

  /// No description provided for @showAllDay.
  ///
  /// In en, this message translates to:
  /// **'Show all day'**
  String get showAllDay;

  /// No description provided for @list.
  ///
  /// In en, this message translates to:
  /// **'List'**
  String get list;

  /// No description provided for @addNew.
  ///
  /// In en, this message translates to:
  /// **'Add New'**
  String get addNew;

  /// No description provided for @customerTransactions.
  ///
  /// In en, this message translates to:
  /// **'Customer Transactions'**
  String get customerTransactions;

  /// No description provided for @open.
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get open;

  /// No description provided for @commercial.
  ///
  /// In en, this message translates to:
  /// **'Commercial'**
  String get commercial;

  /// No description provided for @individual.
  ///
  /// In en, this message translates to:
  /// **'Individual'**
  String get individual;

  /// No description provided for @companyType.
  ///
  /// In en, this message translates to:
  /// **'Company Type'**
  String get companyType;

  /// No description provided for @taxOffice.
  ///
  /// In en, this message translates to:
  /// **'Tax Office'**
  String get taxOffice;

  /// No description provided for @taxNumber.
  ///
  /// In en, this message translates to:
  /// **'Tax Number'**
  String get taxNumber;

  /// No description provided for @customerPhone.
  ///
  /// In en, this message translates to:
  /// **'Customer Phone'**
  String get customerPhone;

  /// No description provided for @webAddress.
  ///
  /// In en, this message translates to:
  /// **'Web Address'**
  String get webAddress;

  /// No description provided for @animationColors.
  ///
  /// In en, this message translates to:
  /// **'Animation Colors'**
  String get animationColors;

  /// No description provided for @serviceIp.
  ///
  /// In en, this message translates to:
  /// **'Service IP'**
  String get serviceIp;

  /// No description provided for @servicePort.
  ///
  /// In en, this message translates to:
  /// **'Service Port'**
  String get servicePort;

  /// No description provided for @companyUpdate.
  ///
  /// In en, this message translates to:
  /// **'Company Update'**
  String get companyUpdate;

  /// No description provided for @regionalTime.
  ///
  /// In en, this message translates to:
  /// **'Regional Time'**
  String get regionalTime;

  /// No description provided for @avenueStreet.
  ///
  /// In en, this message translates to:
  /// **'Avenue/Street'**
  String get avenueStreet;

  /// No description provided for @buildingDoor.
  ///
  /// In en, this message translates to:
  /// **'Building/Door No'**
  String get buildingDoor;

  /// No description provided for @addressDirections.
  ///
  /// In en, this message translates to:
  /// **'Address Directions'**
  String get addressDirections;

  /// No description provided for @country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// No description provided for @kmh.
  ///
  /// In en, this message translates to:
  /// **'Km/h'**
  String get kmh;

  /// No description provided for @performance.
  ///
  /// In en, this message translates to:
  /// **'Performance'**
  String get performance;

  /// No description provided for @permits.
  ///
  /// In en, this message translates to:
  /// **'Permits'**
  String get permits;

  /// No description provided for @costs.
  ///
  /// In en, this message translates to:
  /// **'Costs'**
  String get costs;

  /// No description provided for @selectDevice.
  ///
  /// In en, this message translates to:
  /// **'Select Device'**
  String get selectDevice;

  /// No description provided for @contracted.
  ///
  /// In en, this message translates to:
  /// **'Contracted'**
  String get contracted;

  /// No description provided for @shortTerm.
  ///
  /// In en, this message translates to:
  /// **'Short Term'**
  String get shortTerm;

  /// No description provided for @fullTime.
  ///
  /// In en, this message translates to:
  /// **'Full Time'**
  String get fullTime;

  /// No description provided for @partTime.
  ///
  /// In en, this message translates to:
  /// **'Part Time'**
  String get partTime;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'tr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'tr':
      return AppLocalizationsTr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
