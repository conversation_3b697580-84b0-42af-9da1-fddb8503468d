import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/i18n/strings.g.dart';

final groupsProvider =
    StateNotifierProvider<GroupsNotifier, List<String>>((ref) {
  return GroupsNotifier();
});

class GroupsNotifier extends StateNotifier<List<String>> {
  GroupsNotifier()
      : super(<String>[
          t.group.groupA,
          t.group.groupB,
          t.group.groupC,
          t.group.groupD,
        ]);

  void addGroup(String groupName) {
    state = [...state, groupName];
  }
}
