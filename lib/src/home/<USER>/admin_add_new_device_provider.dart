import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/i18n/strings.g.dart';

final deviceActiveProvider = StateProvider<bool>((ref) => true);

final brandsProvider = Provider<List<String>>((ref) {
  return <String>[
    t.device.brands.teltonika,
    t.device.brands.armoli,
    t.device.brands.kingwoiot,
  ];
});

final modelsProvider = Provider<List<String>>((ref) {
  return <String>[
    t.device.models.modelA,
    t.device.models.modelB,
    t.device.models.modelC,
  ];
});

final simCardOperatorProvider = Provider<List<String>>((ref) {
  return <String>[
    t.device.simOperators.vodafone,
    t.device.simOperators.turkcell,
    t.device.simOperators.turkTelekom,
  ];
});

final connectedClientsProvider = Provider<List<String>>((ref) {
  return <String>[
    t.customer.customerA,
    t.customer.customerB,
    t.customer.customerC,
  ];
});

final carBrandsProvider = Provider<List<String>>((ref) {
  return <String>[
    t.vehicle.brands.mercedes,
    t.vehicle.brands.bmw,
    t.vehicle.brands.audi,
  ];
});

final carModelsProvider = Provider<List<String>>((ref) {
  return <String>[
    t.device.models.modelA,
    t.device.models.modelB,
    t.device.models.modelC,
  ];
});

final onlineOfflineProvider = Provider<List<String>>((ref) {
  return <String>[
    t.common.online,
    t.common.offline,
  ];
});
