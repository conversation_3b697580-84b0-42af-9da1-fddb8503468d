import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

enum TaskTypeEnum {
  visit, // ziyaret
  collection, // tahsilat
  service, // servis
  onlineMeeting, // online toplantı
  phoneCall, // telefon görüşmesi
}

extension TaskTypeEnumX on TaskTypeEnum {
  String label(BuildContext context) {
    switch (this) {
      case TaskTypeEnum.visit:
        return 'Ziyaret'.hardcoded;
      case TaskTypeEnum.collection:
        return 'Tahsilat'.hardcoded;
      case TaskTypeEnum.service:
        return 'Servis'.hardcoded;
      case TaskTypeEnum.onlineMeeting:
        return 'Online Toplantı'.hardcoded;
      case TaskTypeEnum.phoneCall:
        return context.tr.phoneCall;
    }
  }
}
