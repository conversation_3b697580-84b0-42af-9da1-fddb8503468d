import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum TimeRangeOption {
  lastHour1,
  lastHour2,
  lastHour3,
  lastHour6,
  lastHour12,
  lastHour24,
  customRange,
}

extension TimeRangeOptionX on TimeRangeOption {
  bool get isCustomRange => this == TimeRangeOption.customRange;

  String label(BuildContext context) {
    switch (this) {
      case TimeRangeOption.lastHour1:
        return context.tr.lastHour(1);
      case TimeRangeOption.lastHour2:
        return context.tr.lastHour(2);
      case TimeRangeOption.lastHour3:
        return context.tr.lastHour(3);
      case TimeRangeOption.lastHour6:
        return context.tr.lastHour(6);
      case TimeRangeOption.lastHour12:
        return context.tr.lastHour(12);
      case TimeRangeOption.lastHour24:
        return context.tr.lastHour(24);
      case TimeRangeOption.customRange:
        return context.tr.timeInterval;
    }
  }
}

class DisplayTimeRangeOption {
  DisplayTimeRangeOption({
    required this.option,
    required BuildContext context,
  }) : label = option.label(context);

  final TimeRangeOption option;
  final String label;

  @override
  String toString() => label;

  static List<DisplayTimeRangeOption> getList(BuildContext context) {
    return TimeRangeOption.values
        .map(
          (option) => DisplayTimeRangeOption(
            option: option,
            context: context,
          ),
        )
        .toList();
  }
}
