import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:smart_team_web/localization/app_localizations.dart';

extension BuildContextX on BuildContext {
  AppLocalizations get tr {
    // if no locale was found, returns a default
    return AppLocalizations.of(this);
  }

  String? getQueryRouteParameter(String key) {
    return routeData.queryParams.optString(key);
  }

  String? getPathRouteParameter(String key) {
    return routeData.params.optString(key);
  }
}
