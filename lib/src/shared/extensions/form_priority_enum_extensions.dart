import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';

extension FormPriorityEnumX on FormPriorityEnum {
  // TODO: Add localization
  String getLocalizedName() {  
    switch (this) {
      case FormPriorityEnum.low:
        return 'Düşük'.hardcoded; 
      case FormPriorityEnum.medium:
        return 'Orta'.hardcoded;
      case FormPriorityEnum.high:
        return '<PERSON>üksek'.hardcoded;
    }
  }
}
