import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/localization/app_localization_provider.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/app_locale_extension.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';

class LanguageSwitcher extends ConsumerStatefulWidget {
  const LanguageSwitcher({super.key});

  @override
  ConsumerState<LanguageSwitcher> createState() => _LanguageSwitcherState();
}

class _LanguageSwitcherState extends ConsumerState<LanguageSwitcher> {
  @override
  Widget build(BuildContext context) {
    final currentLocale = ref.watch(languageProvider);
    const locales = AppLocale.values;

    return InkWell(
      onTap: ref.read(languageProvider.notifier).toggleLocale,
      child: Container(
        width: 100,
        height: 35,
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(18),
        ),
        child: Stack(
          children: [
            AnimatedAlign(
              alignment: currentLocale == AppLocale.tr ? Alignment.centerRight : Alignment.centerLeft,
              duration: const Duration(milliseconds: 300),
              child: Container(
                width: 50,
                height: 35,
                margin: const EdgeInsets.all(1),
                decoration: BoxDecoration(
                  color: AColor.primaryColor.withAlpha(200),
                  borderRadius: BorderRadius.circular(18),
                ),
              ),
            ),
            Positioned.fill(
              child: Row(
                children: List.generate(
                  locales.length,
                  (index) => Expanded(
                    child: Center(
                      child: Text(
                        locales[index].languageCode.toUpperCase(),
                        style: ATextStyle.text14SemiBold.copyWith(
                          color: currentLocale == locales[index] ? AColor.white : AColor.black,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
