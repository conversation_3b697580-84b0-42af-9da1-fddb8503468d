import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/localization/app_localization_provider.dart';
import 'package:smart_team_web/src/home/<USER>/widgets/hover_colored_icon.dart';
import 'package:smart_team_web/src/shared/enums/date_time_picker_mode.dart';
import 'package:smart_team_web/src/shared/extensions/app_locale_extension.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

class DateTimePickerField extends HookConsumerWidget {
  const DateTimePickerField({
    super.key,
    this.controller,
    this.onChanged,
    this.pickerMode = DateTimePickerMode.dateAndTime,
    this.initialValue,
    this.header,
  });

  /// Controller for the text field (optional).
  final TextEditingController? controller;

  /// Emits a real DateTime whenever the user picks date/time.
  final ValueChanged<DateTime>? onChanged;

  /// Which picker(s) to show.
  final DateTimePickerMode pickerMode;

  /// Pre‐populate the picker and field with this value.
  final DateTime? initialValue;

  /// Field header label.
  final String? header;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final effectiveController = controller ?? useTextEditingController();

    // Hold the selections separately:
    final selectedDate = useState<DateTime?>(initialValue);
    final selectedTime = useState<TimeOfDay?>(
      initialValue != null
          ? TimeOfDay(hour: initialValue!.hour, minute: initialValue!.minute)
          : null,
    );

    // Watch your app’s locale for the date‐picker dialog:
    final locale = ref.watch(languageProvider);

    void updateController() {
      DateTime? combined;

      switch (pickerMode) {
        case DateTimePickerMode.date:
          if (selectedDate.value != null) {
            effectiveController.text =
                '${selectedDate.value!.day}/${selectedDate.value!.month}/${selectedDate.value!.year}';
            combined = selectedDate.value;
          }
          break;

        case DateTimePickerMode.time:
          if (selectedTime.value != null) {
            effectiveController.text = selectedTime.value!.format(context);
            final now = DateTime.now();
            combined = DateTime(
              now.year,
              now.month,
              now.day,
              selectedTime.value!.hour,
              selectedTime.value!.minute,
            );
          }
          break;

        case DateTimePickerMode.dateAndTime:
          final dateStr = selectedDate.value != null
              ? '${selectedDate.value!.day}/${selectedDate.value!.month}/${selectedDate.value!.year}'
              : '';
          final timeStr = selectedTime.value != null
              ? selectedTime.value!.format(context)
              : '';
          final separator =
              (dateStr.isNotEmpty && timeStr.isNotEmpty) ? ' ' : '';
          effectiveController.text = dateStr + separator + timeStr;

          if (selectedDate.value != null && selectedTime.value != null) {
            combined = DateTime(
              selectedDate.value!.year,
              selectedDate.value!.month,
              selectedDate.value!.day,
              selectedTime.value!.hour,
              selectedTime.value!.minute,
            );
          }
          break;
      }

      // Emit the real DateTime if both parts are ready:
      if (combined != null) {
        onChanged?.call(combined);
      }
    }

    // Initialize field if an initialValue was provided:
    useEffect(() {
      if (initialValue != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          selectedDate.value = initialValue;
          selectedTime.value = TimeOfDay(
            hour: initialValue!.hour,
            minute: initialValue!.minute,
          );
          updateController();
        });
      }
      return null;
    }, [initialValue]);

    Future<void> pickDate() async {
      final now = DateTime.now();
      final picked = await showDatePicker(
        context: context,
        locale: locale == AppLocale.en
            ? const Locale('en', 'EN')
            : const Locale('tr', 'TR'),
        initialDate: selectedDate.value ?? now,
        firstDate: DateTime(now.year - 5),
        lastDate: DateTime(now.year + 5),
      );
      if (picked != null) {
        selectedDate.value = picked;
        updateController();
      }
    }

    Future<void> pickTime() async {
      final initial = selectedTime.value ?? TimeOfDay.now();
      final picked = await showTimePicker(
        context: context,
        initialTime: initial,
      );
      if (picked != null) {
        selectedTime.value = picked;
        updateController();
      }
    }

    return CustomTextFormField(
      headerText: header,
      controller: effectiveController,
      readOnly: true,
      onTap: () async {
        switch (pickerMode) {
          case DateTimePickerMode.date:
            await pickDate();
            break;
          case DateTimePickerMode.time:
            await pickTime();
            break;
          case DateTimePickerMode.dateAndTime:
            await pickDate();
            await pickTime();
            break;
        }
      },
      suffixIcon: Padding(
        padding: const EdgeInsets.only(right: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          spacing: 8,
          children: [
            if (pickerMode == DateTimePickerMode.date ||
                pickerMode == DateTimePickerMode.dateAndTime)
              HoverColoredIcon(
                icon: Icons.calendar_month_rounded,
                onSelected: (_) => pickDate(),
              ),
            if (pickerMode == DateTimePickerMode.time ||
                pickerMode == DateTimePickerMode.dateAndTime)
              HoverColoredIcon(
                icon: Icons.watch_later_outlined,
                onSelected: (_) => pickTime(),
              ),
          ],
        ),
      ),
    );
  }
}
