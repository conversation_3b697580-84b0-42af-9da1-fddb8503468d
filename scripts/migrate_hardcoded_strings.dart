#!/usr/bin/env dart

import 'dart:io';

/// Script to help migrate hardcoded strings to slang translations
/// Usage: dart scripts/migrate_hardcoded_strings.dart

void main() {
  print('🔄 Hardcoded String Migration Helper');
  print('=====================================');
  
  // Common replacements map
  final replacements = <String, String>{
    // Common words
    "'Ad'.hardcoded": "context.t.common.name",
    "'Açıklama'.hardcoded": "context.t.common.description", 
    "'Aktif'.hardcoded": "context.t.common.active",
    "'Ara'.hardcoded": "context.t.common.search",
    "'Ekle'.hardcoded": "context.t.common.add",
    "'Düzenle'.hardcoded": "context.t.common.edit",
    "'Sil'.hardcoded": "context.t.common.delete",
    "'Kaydet'.hardcoded": "context.t.common.save",
    "'İptal'.hardcoded": "context.t.common.cancel",
    "'Online'.hardcoded": "context.t.common.online",
    "'Offline'.hardcoded": "context.t.common.offline",
    "'Admin'.hardcoded": "context.t.common.admin",
    
    // Priority
    "'Düşük'.hardcoded": "context.t.priority.low",
    "'Orta'.hardcoded": "context.t.priority.medium",
    "'Yüksek'.hardcoded": "context.t.priority.high",
    
    // Task types
    "'Ziyaret'.hardcoded": "context.t.taskType.visit",
    "'Tahsilat'.hardcoded": "context.t.taskType.collection",
    "'Servis'.hardcoded": "context.t.taskType.service",
    "'Online Toplantı'.hardcoded": "context.t.taskType.onlineMeeting",
    "'Telefon Görüşmesi'.hardcoded": "context.t.taskType.phoneCall",
    
    // Device brands
    "'Teltonika'.hardcoded": "context.t.device.brands.teltonika",
    "'Armoli'.hardcoded": "context.t.device.brands.armoli",
    "'Kingwoiot'.hardcoded": "context.t.device.brands.kingwoiot",
    
    // SIM operators
    "'Vodafone'.hardcoded": "context.t.device.simOperators.vodafone",
    "'Turkcell'.hardcoded": "context.t.device.simOperators.turkcell",
    "'Türk Telekom'.hardcoded": "context.t.device.simOperators.turkTelekom",
    
    // Vehicle brands
    "'Mercedes'.hardcoded": "context.t.vehicle.brands.mercedes",
    "'BMW'.hardcoded": "context.t.vehicle.brands.bmw",
    "'Audi'.hardcoded": "context.t.vehicle.brands.audi",
    
    // Customers
    "'A Müşterisi'.hardcoded": "context.t.customer.customerA",
    "'B Müşterisi'.hardcoded": "context.t.customer.customerB", 
    "'C Müşterisi'.hardcoded": "context.t.customer.customerC",
    "'Müşteriler'.hardcoded": "context.t.customer.customers",
    "'Müşteri Ekle'.hardcoded": "context.t.customer.addCustomer",
    "'Telefon Numarası'.hardcoded": "context.t.customer.phoneNumber",
    "'Açık Adres'.hardcoded": "context.t.customer.openAddress",
    "'Konumumu Bul'.hardcoded": "context.t.customer.findMyLocation",
    "'Tüm alanları doldurunuz'.hardcoded": "context.t.customer.fillAllFields",
    
    // Groups
    "'Group A'.hardcoded": "context.t.group.groupA",
    "'Group B'.hardcoded": "context.t.group.groupB",
    "'Group C'.hardcoded": "context.t.group.groupC", 
    "'Group D'.hardcoded": "context.t.group.groupD",
    "'Grup A'.hardcoded": "context.t.group.groupA",
    "'Grup B'.hardcoded": "context.t.group.groupB",
    "'Grup C'.hardcoded": "context.t.group.groupC",
    "'Grup D'.hardcoded": "context.t.group.groupD",
    
    // Dashboard
    "'Todo List'.hardcoded": "context.t.dashboard.todoList",
    "'Yapılacaklar Listesi'.hardcoded": "context.t.dashboard.todoList",
    "'Anlık Hareketler'.hardcoded": "context.t.dashboard.instantMovements",
    "'Anlık Durma'.hardcoded": "context.t.dashboard.instantStoppage",
    
    // Areas
    "'Alana Giriş'.hardcoded": "context.t.area.areaEntry",
    "'Alanda Bekleme'.hardcoded": "context.t.area.areaWaiting",
    "'Alandan Çıkış'.hardcoded": "context.t.area.areaExit",
    "'Alanı Düzenle'.hardcoded": "context.t.area.editArea",
    "'Alan Adı'.hardcoded": "context.t.area.areaName",
    "'Alan Tipi'.hardcoded": "context.t.area.areaType",
    
    // Emergency
    "'Acil Durum Adı-Soyadı'.hardcoded": "context.t.emergency.emergencyContactName",
    "'Acil Durum Telefonu'.hardcoded": "context.t.emergency.emergencyPhone",
    "'Acil Durumda Aranacak Kişi'.hardcoded": "context.t.emergency.emergencyContact",
    
    // Validation
    "'Link açılamadı'.hardcoded": "context.t.validation.linkCouldNotOpen",
    "'+90 555 555 55 55'.hardcoded": "context.t.validation.phoneNumberExample",
    "'555 555 55 55'.hardcoded": "context.t.validation.phoneNumberShort",
    
    // Copyright
    "'Akıllı Konum Teknolojileri A.Ş. © 2025'.hardcoded": "context.t.copyright",
    
    // Device/Vehicle
    "'Cihaz Ekle'.hardcoded": "context.t.device.addDevice",
    "'Cihaz Listesi'.hardcoded": "context.t.device.deviceList",
    "'Aktivasyon Tarihi'.hardcoded": "context.t.device.activationDate",
    "'Araç Ekle'.hardcoded": "context.t.vehicle.addVehicle",
    "'Araç Listesi'.hardcoded": "context.t.vehicle.vehicleList",
    "'Araç Markası'.hardcoded": "context.t.vehicle.vehicleBrand",
    "'Araç Modeli'.hardcoded": "context.t.vehicle.vehicleModel",
    "'Araç Tipi'.hardcoded": "context.t.vehicle.vehicleType",
    
    // Forms
    "'Açık Form'.hardcoded": "context.t.form.openForm",
    "'Açık form, herkes tarafından görülebilir ve kişilere atanamaz'.hardcoded": "context.t.form.openFormDescription",
    "'* Indicates a required question'.hardcoded": "context.t.form.requiredQuestion",
    "'Add option'.hardcoded": "context.t.form.addOption",
  };
  
  print('📋 Available replacements: ${replacements.length}');
  print('');
  print('To use this script:');
  print('1. Run: find lib/ -name "*.dart" -exec grep -l "\\.hardcoded" {} \\;');
  print('2. For each file, manually replace hardcoded strings with slang translations');
  print('3. Add import: import \'package:smart_team_web/i18n/strings.g.dart\';');
  print('4. Remove import: import \'package:smart_team_web/src/shared/extensions/string_hardcoded.dart\';');
  print('');
  print('🔍 Common replacements:');
  replacements.forEach((key, value) {
    print('  $key -> $value');
  });
  
  print('');
  print('💡 Tips:');
  print('- Use context.t.* for UI widgets that rebuild on locale change');
  print('- Use t.* for simple cases that don\'t need rebuilding');
  print('- Run "make slang" after adding new translations');
  print('- Test language switching after migration');
}
